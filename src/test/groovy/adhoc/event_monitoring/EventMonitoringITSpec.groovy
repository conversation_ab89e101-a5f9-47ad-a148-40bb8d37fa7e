package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams

import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class EventMonitoringITSpec extends BaseAdhocITSpec {

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
    }

    def cleanup() {
        cleanupCommon()
    }

    /**
     * Should detects and processes events from new blockchain blocks
     * Verifies service correctly detects and processes events
     * Expected: Events extracted, parsed, and saved to DynamoDB
     */
    def "Should detects and processes events from new blockchain blocks"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"
        // Create proper mock notifications with blocks that have transactions and events
        def mockNotifications = createMockNewHeadsNotifications()
        // Setup mock Web3j to return blocks with transactions and events
        setupMockWeb3jWithEvents()
        setUpEventStream(mockNotifications)

        // Setup mock Web3j to return pending events
        setUpPendingEvent(createMockPendingEventLogs())

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts and processes ABI files successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Monitoring events...") }

        and: "Block processing is completed successfully"
        // Verify that blocks were processed (even without events)
        println("All log messages:")
        messages.each { msg -> println("  - ${msg}") }

        and: "Provider events are processed and saved to DynamoDB"
        // Check if events were saved to DynamoDB
        def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
        println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
        if (eventsInDb != null) {
            eventsInDb.each { event ->
                println("Event: ${event}")
            }
        }
        // Should have events if ABI parser can match the signatures
        println("Expected: Events should be created if ABI parsing succeeds")

        and: "Block height is updated"
        def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        println("Block height in DynamoDB: ${blockHeightInDb}")
        // Verify that block height was processed
        blockHeightInDb != null
    }

    /**
     * Creates mock NewHeadsNotifications for testing
     * @return List of mock NewHeadsNotification objects
     */
    private List<NewHeadsNotification> createMockNewHeadsNotifications() {
        def notifications = []

        // Create notification 1 - block 1000
        def notification1 = Mock(NewHeadsNotification)
        def params1 = Mock(NotificationParams)
        def result1 = Mock(NewHead)
        result1.getNumber() >> "0x3e8" // 1000 in hex

        notification1.getParams() >> params1
        params1.getResult() >> result1
        notifications.add(notification1)

        // Create notification 2 - block 1001
        def notification2 = Mock(NewHeadsNotification)
        def params2 = Mock(NotificationParams)
        def result2 = Mock(NewHead)
        result2.getNumber() >> "0x3e9" // 1001 in hex

        notification2.getParams() >> params2
        params2.getResult() >> result2
        notifications.add(notification2)

        // Create notification 3 - block 1002
        def notification3 = Mock(NewHeadsNotification)
        def params3 = Mock(NotificationParams)
        def result3 = Mock(NewHead)
        result3.getNumber() >> "0x3ea" // 1002 in hex

        notification3.getParams() >> params3
        params3.getResult() >> result3
        notifications.add(notification3)

        return notifications
    }

    /**
     * Setup mock Web3j to return blocks with transactions and events
     */
    private void setupMockWeb3jWithEvents() {
        // Mock ethGetBlockByNumber to return blocks with transactions
        def mockRequest = Mock(Request)
        def mockEthBlock = Mock(EthBlock)
        def mockBlock = Mock(EthBlock.Block)

        // Create mock transaction results
        def mockTxResult1 = Mock(EthBlock.TransactionResult)
        def mockTxResult2 = Mock(EthBlock.TransactionResult)

        mockTxResult1.get() >> "0xabc123"
        mockTxResult2.get() >> "0xdef456"

        // Setup block with transactions
        mockBlock.getNumber() >> BigInteger.valueOf(1000)
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750608026L) // Fixed timestamp for testing
        mockBlock.getTransactions() >> [mockTxResult1, mockTxResult2]

        mockEthBlock.getBlock() >> mockBlock
        mockRequest.sendAsync() >> CompletableFuture.completedFuture(mockEthBlock)

        // Mock transaction receipts with proper Provider events
        def mockReceiptRequest1 = Mock(Request)
        def mockReceiptRequest2 = Mock(Request)
        def mockReceiptResponse1 = Mock(EthGetTransactionReceipt)
        def mockReceiptResponse2 = Mock(EthGetTransactionReceipt)
        def mockReceipt1 = Mock(TransactionReceipt)
        def mockReceipt2 = Mock(TransactionReceipt)

        // Create proper Provider contract logs
        def mockLog1 = createAddProviderRoleLog()
        def mockLog2 = createAddTokenByProviderLog()

        mockReceipt1.getLogs() >> [mockLog1]
        mockReceipt2.getLogs() >> [mockLog2]

        mockReceiptResponse1.getTransactionReceipt() >> Optional.of(mockReceipt1)
        mockReceiptResponse2.getTransactionReceipt() >> Optional.of(mockReceipt2)

        mockReceiptRequest1.send() >> mockReceiptResponse1
        mockReceiptRequest2.send() >> mockReceiptResponse2

        // Setup Web3j mocks
        web3j.ethGetBlockByNumber(_ as DefaultBlockParameter, true) >> mockRequest
        web3j.ethGetTransactionReceipt(mockTxResult1.get() as String) >> mockReceiptRequest1
        web3j.ethGetTransactionReceipt(mockTxResult2.get() as String) >> mockReceiptRequest2
    }
    /**
     * Create a mock AddProviderRole event for testing
     * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
     */
    private static Event createMockAddProviderRoleEvent() {
        def parameters = [
                // providerId (indexed)
                new TypeReference<Bytes32>(true) {},
                // providerEoa (non-indexed)
                new TypeReference<Address>(false) {},
                // traceId (non-indexed)
                new TypeReference<Bytes32>(false) {}
        ]
        return new Event("AddProviderRole", parameters)
    }

    /**
     * Create a mock AddTokenByProvider event for testing
     * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
     */
    private static Event createMockAddTokenByProviderEvent() {
        def parameters = [
                // providerId (indexed)
                new TypeReference<Bytes32>(true) {},
                // tokenId (non-indexed)
                new TypeReference<Bytes32>(false) {},
                // traceId (non-indexed)
                new TypeReference<Bytes32>(false) {}
        ]
        return new Event("AddTokenByProvider", parameters)
    }

    /**
     * Create a proper AddProviderRole log for testing
     * This matches the exact structure expected by the ABI parser
     */
    private static Log createAddProviderRoleLog() {
        def log = new Log()

        // Use Provider contract address
        log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

        // Calculate AddProviderRole event signature using Web3j
        def addProviderRoleEvent = createMockAddProviderRoleEvent()
        def eventSignature = EventEncoder.encode(addProviderRoleEvent)
        println("AddProviderRole event signature: ${eventSignature}")

        // providerId (indexed parameter)
        def providerId = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

        log.topics = [eventSignature, providerId]

        // Data contains: address providerEoa + bytes32 traceId
        // providerEoa: 0xa1b2c3d4e5f6789012345678901234567890abcd (20 bytes, padded to 32)
        // traceId: 0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef (32 bytes)
        log.data = "0x000000000000000000000000a1b2c3d4e5f6789012345678901234567890abcd" +
                "ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

        log.transactionHash = "0xabc123"
        log.logIndex = BigInteger.valueOf(0)
        log.blockNumber = BigInteger.valueOf(1000)

        return log
    }

    /**
     * Create a proper AddTokenByProvider log for testing
     * This matches the exact structure expected by the ABI parser
     */
    private static Log createAddTokenByProviderLog() {
        def log = new Log()

        // Use Provider contract address
        log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

        // Calculate AddTokenByProvider event signature using Web3j
        def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
        def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
        println("AddTokenByProvider event signature: ${eventSignature}")

        // providerId (indexed parameter)
        def providerId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

        log.topics = [eventSignature, providerId]

        // Data contains: bytes32 tokenId + bytes32 traceId
        // tokenId: 0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
        // traceId: 0x1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff
        log.data = "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba" +
                "1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff"

        log.transactionHash = "0xdef456"
        log.logIndex = BigInteger.valueOf(1)
        log.blockNumber = BigInteger.valueOf(1000)

        return log
    }

    /**
     * Setup mock Web3j to return pending events
     */
    private List createMockPendingEventLogs() {
        def logResults = []

        // Create a mock AddProviderRole log as LogResult
        def addProviderRoleLog = createAddProviderRoleLog()
        addProviderRoleLog.transactionHash = "0xabc134"
        addProviderRoleLog.blockNumber = BigInteger.valueOf(900)
        def logResult1 = Mock(EthLog.LogResult)
        logResult1.get() >> addProviderRoleLog
        logResults.add(logResult1)

        // Create a mock AddTokenByProvider log as LogResult
        def addTokenByProviderLog = createAddTokenByProviderLog()
        addTokenByProviderLog.transactionHash = "0xabc135"
        addTokenByProviderLog.blockNumber = BigInteger.valueOf(900)
        def logResult2 = Mock(EthLog.LogResult)
        logResult2.get() >> addTokenByProviderLog
        logResults.add(logResult2)

        // Create mock block with timestamp
        def mockRequest = Mock(Request)
        def ethBlock = Mock(EthBlock)
        def mockBlock = Mock(EthBlock.Block)

        ethBlock.getBlock() >> mockBlock
        mockRequest.send() >> ethBlock
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750606026L) // Fixed timestamp for testing
        web3j.ethGetBlockByNumber(_ as DefaultBlockParameter, false) >> mockRequest

        return logResults
    }

}