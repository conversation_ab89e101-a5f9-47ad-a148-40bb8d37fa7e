package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import adhoc.mock.EventMockFactory
import adhoc.mock.Web3jMockHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams

import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class EventMonitoringITSpec extends BaseAdhocITSpec {

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    // Create instances of mock factories
    EventMockFactory eventMockFactory = new EventMockFactory()
    Web3jMockHelper web3jMockHelper = new Web3jMockHelper()

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
    }

    def cleanup() {
        cleanupCommon()
    }

    /**
     * Should detects and processes events from new blockchain blocks
     * Verifies service correctly detects and processes events
     * Expected: Events extracted, parsed, and saved to DynamoDB
     */
    def "Should detects and processes events from new blockchain blocks"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"
        // Create proper mock notifications with blocks that have transactions and events
        // Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
        def mockCreator = { Mock(it) }
        def mockNotifications = web3jMockHelper.createMockNewHeadsNotifications(1000L, 3, mockCreator)
        // Setup mock Web3j to return blocks with transactions and events
//        setupMockWeb3jWithEvents()
        web3jMockHelper.setupMockWeb3jWithEvents(web3j, eventMockFactory, mockCreator)

        setUpEventStream(mockNotifications)

        // Setup mock Web3j to return pending events
        setUpPendingEvent(createMockPendingEventLogs())

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts and processes ABI files successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Monitoring events...") }

        and: "Block processing is completed successfully"
        assert messages.any { it.contains("Event found tx_hash=") }
        assert messages.any { it.contains("Event parsed tx_hash=") }
        assert messages.any { it.contains("Success to register event") }

        and: "Provider events are processed and saved to DynamoDB"
        // Check if events were saved to DynamoDB
        def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
        println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
        if (eventsInDb != null) {
            eventsInDb.each { event ->
                println("Event: ${event}")
            }
        }

        and: "Block height is updated"
        def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        println("Block height in DynamoDB: ${blockHeightInDb}")
        // Verify that block height was processed
        blockHeightInDb != null
    }

    /**
     * Setup mock Web3j to return blocks with transactions and events
     */
    private void setupMockWeb3jWithEvents() {
        // Mock ethGetBlockByNumber to return blocks with transactions
        def mockRequest = Mock(Request)
        def mockEthBlock = Mock(EthBlock)
        def mockBlock = Mock(EthBlock.Block)

        // Create mock transaction results
        def mockTxResult1 = Mock(EthBlock.TransactionResult)
        def mockTxResult2 = Mock(EthBlock.TransactionResult)

        mockTxResult1.get() >> "0xabc123"
        mockTxResult2.get() >> "0xdef456"

        // Setup block with transactions
        mockBlock.getNumber() >> BigInteger.valueOf(1000)
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750608026L) // Fixed timestamp for testing
        mockBlock.getTransactions() >> [mockTxResult1, mockTxResult2]

        mockEthBlock.getBlock() >> mockBlock
        mockRequest.sendAsync() >> CompletableFuture.completedFuture(mockEthBlock)

        // Mock transaction receipts with proper Provider events
        def mockReceiptRequest1 = Mock(Request)
        def mockReceiptRequest2 = Mock(Request)
        def mockReceiptResponse1 = Mock(EthGetTransactionReceipt)
        def mockReceiptResponse2 = Mock(EthGetTransactionReceipt)
        def mockReceipt1 = Mock(TransactionReceipt)
        def mockReceipt2 = Mock(TransactionReceipt)

        // Create proper Provider contract logs using EventMockFactory
        def mockLog1 = eventMockFactory.createAddProviderRoleLog()
        def mockLog2 = eventMockFactory.createAddTokenByProviderLog()

        mockReceipt1.getLogs() >> [mockLog1]
        mockReceipt2.getLogs() >> [mockLog2]

        mockReceiptResponse1.getTransactionReceipt() >> Optional.of(mockReceipt1)
        mockReceiptResponse2.getTransactionReceipt() >> Optional.of(mockReceipt2)

        mockReceiptRequest1.send() >> mockReceiptResponse1
        mockReceiptRequest2.send() >> mockReceiptResponse2

        // Setup Web3j mocks
        web3j.ethGetBlockByNumber(!null as DefaultBlockParameter, true) >> mockRequest
        web3j.ethGetTransactionReceipt(mockTxResult1.get() as String) >> mockReceiptRequest1
        web3j.ethGetTransactionReceipt(mockTxResult2.get() as String) >> mockReceiptRequest2
    }

    /**
     * Create mock pending event logs for testing
     * @return List of EthLog.LogResult objects containing mock event logs
     */
    private List createMockPendingEventLogs() {
        def logResults = []

        // Create a mock AddProviderRole log as LogResult
        def addProviderRoleLog = eventMockFactory.createAddProviderRoleLog()
        addProviderRoleLog.transactionHash = "0xabc134"
        addProviderRoleLog.blockNumber = BigInteger.valueOf(900)
        def logResult1 = Mock(EthLog.LogResult)
        logResult1.get() >> addProviderRoleLog
        logResults.add(logResult1)

        // Create a mock AddTokenByProvider log as LogResult
        def addTokenByProviderLog = eventMockFactory.createAddTokenByProviderLog()
        addTokenByProviderLog.transactionHash = "0xabc135"
        addTokenByProviderLog.blockNumber = BigInteger.valueOf(900)
        def logResult2 = Mock(EthLog.LogResult)
        logResult2.get() >> addTokenByProviderLog
        logResults.add(logResult2)

        // Create mock block with timestamp
        def mockRequest = Mock(Request)
        def ethBlock = Mock(EthBlock)
        def mockBlock = Mock(EthBlock.Block)

        ethBlock.getBlock() >> mockBlock
        mockRequest.send() >> ethBlock
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750606026L) // Fixed timestamp for testing
        web3j.ethGetBlockByNumber(!null as DefaultBlockParameter, false) >> mockRequest

        return logResults
    }
}