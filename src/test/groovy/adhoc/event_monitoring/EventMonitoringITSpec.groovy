package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import adhoc.mock.EventMockFactory
import adhoc.mock.Web3jMockHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

import java.util.concurrent.TimeUnit

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class EventMonitoringITSpec extends BaseAdhocITSpec {

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
    }

    def cleanup() {
        cleanupCommon()
    }

    /**
     * Should detects and processes events from new blockchain blocks
     * Verifies service correctly detects and processes events
     * Expected: Events extracted, parsed, and saved to DynamoDB
     */
    def "Should detects and processes events from new blockchain blocks"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"
        // Create proper mock notifications with blocks that have transactions and events
        def mockNotifications = EventMockFactory.createMockNewHeadsNotifications()
        // Setup mock Web3j to return blocks with transactions and events
        Web3jMockHelper.setupMockWeb3jWithEvents(web3j)
        Web3jMockHelper.setUpEventStream(web3j, mockNotifications, scheduler)

        // Setup mock Web3j to return pending events
        def mockPendingEventLogs = EventMockFactory.createMockPendingEventLogs()
        Web3jMockHelper.setUpPendingEvent(web3j, mockPendingEventLogs)

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts and processes ABI files successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Monitoring events...") }

        and: "Block processing is completed successfully"
        assert messages.any { it.contains("event found tx_hash=") }
        assert messages.any { it.contains("event parsed tx_hash=") }
        assert messages.any { it.contains("Success to register event") }

        and: "Provider events are processed and saved to DynamoDB"
        // Check if events were saved to DynamoDB
        def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
        println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
        if (eventsInDb != null) {
            eventsInDb.each { event ->
                println("Event: ${event}")
            }
        }

        and: "Block height is updated"
        def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        println("Block height in DynamoDB: ${blockHeightInDb}")
        // Verify that block height was processed
        blockHeightInDb != null
    }








}