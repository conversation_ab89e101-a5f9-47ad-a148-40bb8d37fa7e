package adhoc.mock

import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams
import spock.lang.Specification

import java.util.concurrent.CompletableFuture
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

/**
 * Helper class for setting up Web3j mock configurations used in testing.
 * This class centralizes Web3j mock setup logic, improving maintainability
 * and reusability across test classes.
 *
 * Note: This class is designed to be used within Spock test classes where Mock() is available.
 */
class Web3jMockHelper extends Specification {


    /**
     * Creates mock NewHeadsNotifications for testing with flexible parameters
     * @param startBlockNumber The starting block number in decimal format
     * @param numberOfNotifications The number of NewHeadsNotification objects to create
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     * @return List of mock NewHeadsNotification objects with sequential block numbers
     */
    def createMockNewHeadsNotifications(long startBlockNumber, int numberOfNotifications, Closure mockCreator) {
        def notifications = []

        for (int i = 0; i < numberOfNotifications; i++) {
            def currentBlockNumber = startBlockNumber + i
            def hexBlockNumber = "0x" + Long.toHexString(currentBlockNumber)

            def notification = mockCreator(NewHeadsNotification)
            def params = mockCreator(NotificationParams)
            def result = mockCreator(NewHead)
            result.getNumber() >> hexBlockNumber

            notification.getParams() >> params
            params.getResult() >> result
            notifications.add(notification)
        }

        return notifications
    }

    /**
     * Setup mock Web3j to return blocks with transactions and events
     * This method must be called from within a Spock test class context
     * @param web3j The Web3j mock instance to configure
     * @param eventMockFactory Instance of EventMockFactory for creating log mocks
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     */
    def setupMockWeb3jWithEvents(Web3j web3j, EventMockFactory eventMockFactory, Closure mockCreator) {
        // Mock ethGetBlockByNumber to return blocks with transactions
        def mockRequest = mockCreator(Request)
        def mockEthBlock = mockCreator(EthBlock)
        def mockBlock = mockCreator(EthBlock.Block)

        // Create mock transaction results
        def mockTxResult1 = mockCreator(EthBlock.TransactionResult)
        def mockTxResult2 = mockCreator(EthBlock.TransactionResult)

        mockTxResult1.get() >> "0xabc123"
        mockTxResult2.get() >> "0xdef456"

        // Setup block with transactions
        mockBlock.getNumber() >> BigInteger.valueOf(1000)
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750608026L) // Fixed timestamp for testing
        mockBlock.getTransactions() >> [mockTxResult1, mockTxResult2]

        mockEthBlock.getBlock() >> mockBlock
        mockRequest.sendAsync() >> CompletableFuture.completedFuture(mockEthBlock)

        // Mock transaction receipts with proper Provider events
        def mockReceiptRequest1 = mockCreator(Request)
        def mockReceiptRequest2 = mockCreator(Request)
        def mockReceiptResponse1 = mockCreator(EthGetTransactionReceipt)
        def mockReceiptResponse2 = mockCreator(EthGetTransactionReceipt)
        def mockReceipt1 = mockCreator(TransactionReceipt)
        def mockReceipt2 = mockCreator(TransactionReceipt)

        // Create proper Provider contract logs using EventMockFactory
        def mockLog1 = eventMockFactory.createAddProviderRoleLog()
        def mockLog2 = eventMockFactory.createAddTokenByProviderLog()

        mockReceipt1.getLogs() >> [mockLog1]
        mockReceipt2.getLogs() >> [mockLog2]

        mockReceiptResponse1.getTransactionReceipt() >> Optional.of(mockReceipt1)
        mockReceiptResponse2.getTransactionReceipt() >> Optional.of(mockReceipt2)

        mockReceiptRequest1.send() >> mockReceiptResponse1
        mockReceiptRequest2.send() >> mockReceiptResponse2

        // Setup Web3j mocks
        web3j.ethGetBlockByNumber(!null as DefaultBlockParameter, true) >> mockRequest
        web3j.ethGetTransactionReceipt(mockTxResult1.get() as String) >> mockReceiptRequest1
        web3j.ethGetTransactionReceipt(mockTxResult2.get() as String) >> mockReceiptRequest2
    }

    /**
     * Setup mock Web3j to return pending events with block timestamp
     * @param web3j The Web3j mock instance to configure
     * @param logResults List of EthLog.LogResult objects to return
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     */
    def setupMockPendingEvents(Web3j web3j, List<EthLog.LogResult> logResults, Closure mockCreator) {
        // Setup mock for ethGetLogs
        def mockLogRequest = mockCreator(Request)
        def mockLog = mockCreator(EthLog)

        web3j.ethGetLogs(!null as EthFilter) >> mockLogRequest
        mockLogRequest.send() >> mockLog
        mockLog.getLogs() >> logResults

        // Create mock block with timestamp for pending events
        def mockBlockRequest = mockCreator(Request)
        def ethBlock = mockCreator(EthBlock)
        def mockBlock = mockCreator(EthBlock.Block)

        ethBlock.getBlock() >> mockBlock
        mockBlockRequest.send() >> ethBlock
        mockBlock.getTimestamp() >> BigInteger.valueOf(1750606026L) // Fixed timestamp for testing
        web3j.ethGetBlockByNumber(!null as DefaultBlockParameter, false) >> mockBlockRequest
    }

    /**
     * Setup event stream for Web3j mock
     * @param web3j The Web3j mock instance to configure
     * @param blocks List of NewHeadsNotification objects to stream
     * @param scheduler ScheduledExecutorService for scheduling mock events
     */
    def setUpEventStream(Web3j web3j, List<NewHeadsNotification> blocks, ScheduledExecutorService scheduler) {
        def index = new AtomicInteger(0)

        // Defer execution until Flowable is subscribed
        def flowable = Flowable.defer( {
            def processor = PublishProcessor.<NewHeadsNotification> create()

            scheduler.scheduleWithFixedDelay({
                int i = index.getAndIncrement()
                if (i < blocks.size()) {
                    processor.onNext(blocks.get(i))
                }
            }, 1, 2, TimeUnit.SECONDS)

            return processor
        })

        // Stub the method to return deferred Flowable
        web3j.newHeadsNotifications() >> flowable
    }

    /**
     * Setup pending event for Web3j mock
     * @param web3j The Web3j mock instance to configure
     * @param resultList List of EthLog.LogResult objects to return
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     */
    def setUpPendingEvent(Web3j web3j, List<EthLog.LogResult> resultList, Closure mockCreator) {
        def mockRequest = mockCreator(Request)
        def mockLog = mockCreator(EthLog)

        web3j.ethGetLogs(!null as EthFilter) >> mockRequest
        mockRequest.send() >> mockLog
        mockLog.getLogs() >> resultList
    }

    /**
     * Setup complete Web3j mock configuration for event monitoring tests
     * This method combines both event stream and pending event setup
     * @param web3j The Web3j mock instance to configure
     * @param scheduler ScheduledExecutorService for scheduling mock events
     * @param eventMockFactory Instance of EventMockFactory for creating mocks
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     */
    def setupCompleteWeb3jMock(Web3j web3j, ScheduledExecutorService scheduler, EventMockFactory eventMockFactory, Closure mockCreator) {
        // Setup mock Web3j with events
        setupMockWeb3jWithEvents(web3j, eventMockFactory, mockCreator)

        // Setup mock pending events
        def mockPendingEventLogs = eventMockFactory.createMockPendingEventLogs(mockCreator)
        setupMockPendingEvents(web3j, mockPendingEventLogs, mockCreator)

        // Setup event stream
        def mockNotifications = eventMockFactory.createMockNewHeadsNotifications(mockCreator)
        setUpEventStream(web3j, mockNotifications, scheduler)

        // Setup pending events (alternative method)
        setUpPendingEvent(web3j, mockPendingEventLogs, mockCreator)
    }
}
