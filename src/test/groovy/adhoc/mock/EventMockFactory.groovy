package adhoc.mock

import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams

/**
 * Factory class for creating mock event objects used in testing.
 * This class centralizes the creation of various mock objects related to blockchain events,
 * improving maintainability and reusability across test classes.
 *
 * Note: This class provides data and configuration for mocks, but the actual Mock() creation
 * must be done within Spock test classes.
 */
class EventMockFactory {

    /**
     * Get block numbers for mock NewHeadsNotifications
     * @return List of hex-encoded block numbers
     */
    static List<String> getBlockNumbers() {
        return ["0x3e8", "0x3e9", "0x3ea"] // 1000, 1001, 1002 in hex
    }

    /**
     * Creates mock NewHeadsNotifications for testing blockchain block notifications
     * This method must be called from within a Spock test class context
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     * @return List of mock NewHeadsNotification objects with predefined block numbers
     */
    def createMockNewHeadsNotifications(Closure mockCreator) {
        def notifications = []
        def blockNumbers = getBlockNumbers()

        blockNumbers.eachWithIndex { blockNumber, index ->
            def notification = mockCreator(NewHeadsNotification)
            def params = mockCreator(NotificationParams)
            def result = mockCreator(NewHead)
            result.getNumber() >> blockNumber

            notification.getParams() >> params
            params.getResult() >> result
            notifications.add(notification)
        }

        return notifications
    }

    /**
     * Create a mock AddProviderRole event definition for testing
     * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
     * @return Event definition for AddProviderRole
     */
    static Event createMockAddProviderRoleEvent() {
        def parameters = [
                // providerId (indexed)
                new TypeReference<Bytes32>(true) {},
                // providerEoa (non-indexed)
                new TypeReference<Address>(false) {},
                // traceId (non-indexed)
                new TypeReference<Bytes32>(false) {}
        ]
        return new Event("AddProviderRole", parameters)
    }

    /**
     * Create a mock AddTokenByProvider event definition for testing
     * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
     * @return Event definition for AddTokenByProvider
     */
    static Event createMockAddTokenByProviderEvent() {
        def parameters = [
                // providerId (indexed)
                new TypeReference<Bytes32>(true) {},
                // tokenId (non-indexed)
                new TypeReference<Bytes32>(false) {},
                // traceId (non-indexed)
                new TypeReference<Bytes32>(false) {}
        ]
        return new Event("AddTokenByProvider", parameters)
    }

    /**
     * Create a proper AddProviderRole log for testing
     * This matches the exact structure expected by the ABI parser
     * @return Log object with AddProviderRole event data
     */
    static Log createAddProviderRoleLog() {
        def log = new Log()

        // Use Provider contract address
        log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

        // Calculate AddProviderRole event signature using Web3j
        def addProviderRoleEvent = createMockAddProviderRoleEvent()
        def eventSignature = EventEncoder.encode(addProviderRoleEvent)
        println("AddProviderRole event signature: ${eventSignature}")

        // providerId (indexed parameter)
        def providerId = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

        log.topics = [eventSignature, providerId]

        // Data contains: address providerEoa + bytes32 traceId
        // providerEoa: 0xa1b2c3d4e5f6789012345678901234567890abcd (20 bytes, padded to 32)
        // traceId: 0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef (32 bytes)
        log.data = "0x000000000000000000000000a1b2c3d4e5f6789012345678901234567890abcd" +
                "ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

        log.transactionHash = "0xabc123"
        log.logIndex = BigInteger.valueOf(0)
        log.blockNumber = BigInteger.valueOf(1000)

        return log
    }

    /**
     * Create a proper AddTokenByProvider log for testing
     * This matches the exact structure expected by the ABI parser
     * @return Log object with AddTokenByProvider event data
     */
    static Log createAddTokenByProviderLog() {
        def log = new Log()

        // Use Provider contract address
        log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

        // Calculate AddTokenByProvider event signature using Web3j
        def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
        def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
        println("AddTokenByProvider event signature: ${eventSignature}")

        // providerId (indexed parameter)
        def providerId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

        log.topics = [eventSignature, providerId]

        // Data contains: bytes32 tokenId + bytes32 traceId
        // tokenId: 0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
        // traceId: 0x1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff
        log.data = "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba" +
                "1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff"

        log.transactionHash = "0xdef456"
        log.logIndex = BigInteger.valueOf(1)
        log.blockNumber = BigInteger.valueOf(1000)

        return log
    }

    /**
     * Create mock pending event logs for testing
     * This method must be called from within a Spock test class context
     * @param mockCreator Closure that creates Mock objects (typically from test class)
     * @return List of EthLog.LogResult objects containing mock event logs
     */
    def createMockPendingEventLogs(Closure mockCreator) {
        def logResults = []

        // Create a mock AddProviderRole log as LogResult
        def addProviderRoleLog = createAddProviderRoleLog()
        addProviderRoleLog.transactionHash = "0xabc134"
        addProviderRoleLog.blockNumber = BigInteger.valueOf(900)
        def logResult1 = mockCreator(EthLog.LogResult)
        logResult1.get() >> addProviderRoleLog
        logResults.add(logResult1)

        // Create a mock AddTokenByProvider log as LogResult
        def addTokenByProviderLog = createAddTokenByProviderLog()
        addTokenByProviderLog.transactionHash = "0xabc135"
        addTokenByProviderLog.blockNumber = BigInteger.valueOf(900)
        def logResult2 = mockCreator(EthLog.LogResult)
        logResult2.get() >> addTokenByProviderLog
        logResults.add(logResult2)

        return logResults
    }
}
